package com.example.project.models;

/**
 * Authentication response models for all auth endpoints
 */
public class AuthResponse {
    private boolean success;
    private String message;
    private User user;
    private String accessToken;
    private String refreshToken;
    private String error;
    
    // Default constructor for Gson
    public AuthResponse() {}
    
    // Constructor for success response
    public AuthResponse(boolean success, String message, User user, String accessToken, String refreshToken) {
        this.success = success;
        this.message = message;
        this.user = user;
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
    }
    
    // Constructor for error response
    public AuthResponse(String error, String message) {
        this.success = false;
        this.error = error;
        this.message = message;
    }
    
    // Getters
    public boolean isSuccess() { return success; }
    public String getMessage() { return message; }
    public User getUser() { return user; }
    public String getAccessToken() { return accessToken; }
    public String getRefreshToken() { return refreshToken; }
    public String getError() { return error; }
    
    // Setters
    public void setSuccess(boolean success) { this.success = success; }
    public void setMessage(String message) { this.message = message; }
    public void setUser(User user) { this.user = user; }
    public void setAccessToken(String accessToken) { this.accessToken = accessToken; }
    public void setRefreshToken(String refreshToken) { this.refreshToken = refreshToken; }
    public void setError(String error) { this.error = error; }
    
    /**
     * Validates the response structure
     */
    public boolean isValidResponse() {
        if (success) {
            return user != null && accessToken != null && !accessToken.trim().isEmpty();
        } else {
            return error != null && !error.trim().isEmpty();
        }
    }
    
    @Override
    public String toString() {
        if (success) {
            return "AuthResponse{" +
                    "success=" + success +
                    ", message='" + message + '\'' +
                    ", user=" + user +
                    ", accessToken='" + (accessToken != null ? "[REDACTED]" : "null") + '\'' +
                    ", refreshToken='" + (refreshToken != null ? "[REDACTED]" : "null") + '\'' +
                    '}';
        } else {
            return "AuthResponse{" +
                    "success=" + success +
                    ", error='" + error + '\'' +
                    ", message='" + message + '\'' +
                    '}';
        }
    }
    
    /**
     * User model for authentication responses
     */
    public static class User {
        private String id;
        private String email;
        private String name;
        private String picture;
        private String type; // "google" or "guest"
        
        public User() {}
        
        public User(String id, String email, String name, String picture, String type) {
            this.id = id;
            this.email = email;
            this.name = name;
            this.picture = picture;
            this.type = type;
        }
        
        // Getters
        public String getId() { return id; }
        public String getEmail() { return email; }
        public String getName() { return name; }
        public String getPicture() { return picture; }
        public String getType() { return type; }
        
        // Setters
        public void setId(String id) { this.id = id; }
        public void setEmail(String email) { this.email = email; }
        public void setName(String name) { this.name = name; }
        public void setPicture(String picture) { this.picture = picture; }
        public void setType(String type) { this.type = type; }
        
        public boolean isGuest() {
            return "guest".equals(type);
        }
        
        public boolean isGoogleUser() {
            return "google".equals(type);
        }
        
        @Override
        public String toString() {
            return "User{" +
                    "id='" + id + '\'' +
                    ", email='" + email + '\'' +
                    ", name='" + name + '\'' +
                    ", picture='" + picture + '\'' +
                    ", type='" + type + '\'' +
                    '}';
        }
    }
}
