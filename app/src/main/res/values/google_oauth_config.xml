<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 
    Google OAuth Configuration
    
    IMPORTANT: You need to replace this with your actual Google OAuth client ID
    
    To get your client ID:
    1. Go to Google Cloud Console (https://console.cloud.google.com/)
    2. Create a new project or select existing project
    3. Enable Google Sign-In API
    4. Go to Credentials > Create Credentials > OAuth 2.0 Client IDs
    5. Select "Android" application type
    6. Add your package name: com.example.project
    7. Add your SHA-1 certificate fingerprint (get it with: keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android)
    8. Copy the generated Client ID here
    -->
    
    <!-- Replace this with your Supabase Google OAuth Client ID -->
    <string name="google_oauth_client_id">845950786787-f2verom4l3rqjhrukmbp9u7bqjikforl.apps.googleusercontent.com</string>

    <!--
    Get this from: Supabase Dashboard > Authentication > Settings > Google Provider
    It should look like: 123456789012-abcdef<PERSON>ijklmnopqrstuvwxyz123456.apps.googleusercontent.com
    -->
    
    <!-- 
    For testing purposes, you can use a placeholder, but real Google Sign-In won't work
    until you configure the proper client ID with your app's package name and SHA-1 fingerprint
    -->
</resources>
